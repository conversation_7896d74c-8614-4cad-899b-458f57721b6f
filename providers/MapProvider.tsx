import * as Location from 'expo-location';
import React, {
  createContext,
  PropsWithChildren,
  RefObject,
  useContext,
  useRef,
  useCallback,
  useMemo,
} from 'react';
import { AppState } from 'react-native';

import { PersonProfileSheetHandle } from '~/components/People/PersonProfileSheet';
import { EventService } from '~/services/EventService';
import { FriendService } from '~/services/FriendService';
import { UserService } from '~/services/UserService';
import { UserStore } from '~/store/store';
import {
  EventType,
  EventContextType,
  coodinates,
  ViewModeType,
  ViewModeMap,
  MapTsType,
  MapTsTypeMap,
} from '~/types';
import { Person } from '~/types/people_type';

const EventProviderContext = createContext<EventContextType>({
  selectedEvent: null,
  setSelectedEvent: () => {},
  filteredEvents: [],
  setSearchQuery: () => {},
  categories: [],
  searchQuery: 'All',
  cameraRef: null,
  zoomToLocation: () => {},
  resetToUserLocation: () => {},
  followUserLocation: true,
  setFollowUserLocation: () => {},
  ViewMode: ViewModeMap.ListView,
  setViewMode: () => {},
  MapType: MapTsTypeMap.Events,
  setMapType: () => {},
  People: [],
  zoomLevel: 14,
  setZoomLevel: () => {},
  userId: null,
  setUserId: () => {},
  personProfileSheetRef: { current: null } as unknown as RefObject<PersonProfileSheetHandle>,
  setPeople: () => {},
  isPersonProfileSheetOpen: false,
  setIsPersonProfileSheetOpen: () => {},
  isLoadingEvents: false,
  isLoadingCategories: false,
  isLoadingPeople: false,
  eventsError: null,
  categoriesError: null,
  peopleError: null,
  fetchEvents: async () => [],
  fetchCategories: async () => [],
  fetchPeople: async () => [],
  isContinuousFetchEnabled: true,
  setIsContinuousFetchEnabled: () => {},
});

// Custom hook to selectively access context values
// This helps prevent unnecessary rerenders when only specific values are needed
export const useEventValue = <K extends keyof EventContextType>(key: K): EventContextType[K] => {
  const context = useContext(EventProviderContext);
  return context[key];
};

export const EventProvider = ({ children }: PropsWithChildren) => {
  const [selectedEvent, setSelectedEvent] = React.useState<EventType | null>(null);
  const [People, setPeople] = React.useState<Person[]>([]);
  const [filteredEvents, setFilteredEvents] = React.useState<EventType[]>([]);
  const [allEvents, setAllEvents] = React.useState<EventType[]>([]);
  const [searchQuery, setSearchQuery] = React.useState<string>('All');
  const [categories, setCategories] = React.useState<string[]>([]);
  const [ViewMode, setViewMode] = React.useState<ViewModeType>(ViewModeMap.ListView);
  const [MapType, setMapType] = React.useState<MapTsType>(MapTsTypeMap.People);
  const [isLoadingEvents, setIsLoadingEvents] = React.useState<boolean>(false);
  const [isLoadingCategories, setIsLoadingCategories] = React.useState<boolean>(false);
  const [isLoadingPeople, setIsLoadingPeople] = React.useState<boolean>(false);
  const [eventsError, setEventsError] = React.useState<string | null>(null);
  const [categoriesError, setCategoriesError] = React.useState<string | null>(null);
  const [peopleError, setPeopleError] = React.useState<string | null>(null);
  const cameraRef = useRef<any>(null);
  const [followUserLocation, setFollowUserLocation] = React.useState(true);
  const [zoomLevel, setZoomLevel] = React.useState<number>(14);
  const [userId, setUserId] = React.useState<string | number | null>(null);
  const personProfileSheetRef = useRef<PersonProfileSheetHandle>(null);
  const [isPersonProfileSheetOpen, setIsPersonProfileSheetOpen] = React.useState(false);
  const [isContinuousFetchEnabled, setIsContinuousFetchEnabled] = React.useState(true);
  const userData = UserStore((state: any) => state.user);

  // Helper function to filter events based on visibility and end date
  const filterEvents = useCallback(async (events: EventType[]) => {
    const userStore = UserStore.getState() as { user: any };
    const currentUser = userStore.user;
    const currentUserId = currentUser?.id;

    if (!currentUserId) {
      // If no current user, only show public events that are not expired
      return events.filter((event) => {
        const isNotExpired = !event.endDateTime || new Date(event.endDateTime) > new Date();
        const isPublic = event.visibility === 'PUBLIC';
        return isNotExpired && isPublic;
      });
    }

    try {
      // Get user's friends list
      const friendsResponse = await FriendService.getFriends(currentUserId);
      let friendIds: string[] = [];

      if (
        friendsResponse.success &&
        friendsResponse.body &&
        Array.isArray(friendsResponse.body.friends)
      ) {
        friendIds = friendsResponse.body.friends.map((friend: any) => friend.id.toString());
      }

      // Filter events based on visibility and end date
      const filteredEvents = events.filter((event) => {
        // Filter out expired events
        const isNotExpired = !event.endDateTime || new Date(event.endDateTime) > new Date();

        if (!isNotExpired) {
          return false;
        }

        // Check visibility
        if (event.visibility === 'PUBLIC') {
          return true; // Public events are visible to everyone
        }

        if (event.visibility === 'PRIVATE') {
          // Private events are only visible to friends and the event creator
          const eventCreatorId = event.user?.id?.toString();
          const isEventCreator = eventCreatorId === currentUserId.toString();
          const isUserFriend = eventCreatorId && friendIds.includes(eventCreatorId);

          const canView = isEventCreator || isUserFriend;

          if (!canView) {
          }

          return canView;
        }

        // Default: if visibility is not specified, treat as public
        return true;
      });

      return filteredEvents;
    } catch (error) {
      console.error('❌ Error filtering events (showing all public events):', error);
      // On error, fall back to showing only public and non-expired events
      return events.filter((event) => {
        const isNotExpired = !event.endDateTime || new Date(event.endDateTime) > new Date();
        const isPublic = event.visibility === 'PUBLIC';
        return isNotExpired && isPublic;
      });
    }
  }, []);

  // Fetch events from API
  const fetchEvents = useCallback(
    async (queryParams: any = {}) => {
      try {
        setIsLoadingEvents(true);
        setEventsError(null);
        const response = await EventService.getEvents(queryParams);

        // Handle the specific API response structure: { body: [...], message: "...", success: true }
        let events: EventType[] = [];
        if (response && Array.isArray(response.body)) {
          // Main case: response.body contains the events array
          events = response.body.map((event: any) => ({
            id: event.id,
            title: event.title,
            user: event.user,
            description: event.description,
            location: event.location,
            locationData: event.locationData,
            eventUploads: event.eventUploads,
            storyImages: event.storyImages,
            promoted: event.promoted,
            promotionalPackage: event.promotionalPackage,
            coverImage:
              event.coverImages && event.coverImages.length > 0
                ? event.coverImages[0].secureUrl || event.coverImages[0].url || null
                : null,
            startDateTime: event.startDateTime,
            endDateTime: event.endDateTime,
            eventType: event.eventCategory?.name || event.eventCategory?.categoryName || 'General',
            visibility: event.visibility,
            isPaid: event.paid,
            ticketSetup: event.ticketSetup,
            owners: event.owners || [],
            currency: event.currency || 'USD',
          }));
        } else if (Array.isArray(response)) {
          // Fallback: direct array response
          events = response;
        } else if (response && Array.isArray(response.data)) {
          // Fallback: response.data contains events
          events = response.data;
        } else if (response && Array.isArray(response.events)) {
          // Fallback: response.events contains events
          events = response.events;
        } else {
          console.warn('Unexpected API response structure:', response);
          events = [];
        }

        // Filter events based on visibility and end date
        const filteredEvents = await filterEvents(events);

        setAllEvents(filteredEvents);
        return filteredEvents;
      } catch (error) {
        console.error('Error fetching events:', error);
        setEventsError(error instanceof Error ? error.message : 'Failed to fetch events');
        // Set empty array on error to prevent undefined issues
        setAllEvents([]);
        return [];
      } finally {
        setIsLoadingEvents(false);
      }
    },
    [filterEvents]
  );

  // Helper function to filter people based on visibility
  const filterPeople = useCallback(async (people: any[]) => {
    const userStore = UserStore.getState() as { user: any };
    const currentUser = userStore.user;
    const currentUserId = currentUser?.id;

    if (!currentUserId) {
      console.warn('No current user found, filtering out all private users');
      // If no current user, only show public users
      return people.filter((person) => {
        const isPublic = !person.visibility || person.visibility === 'PUBLIC';
        return isPublic;
      });
    }

    try {
      // Get user's friends list
      const friendsResponse = await FriendService.getFriends(currentUserId);
      let friendIds: string[] = [];

      if (
        friendsResponse.success &&
        friendsResponse.body &&
        Array.isArray(friendsResponse.body.friends)
      ) {
        friendIds = friendsResponse.body.friends.map((friend: any) => friend.id.toString());
      }

      // Filter people based on visibility
      const filteredPeople = people.filter((person) => {
        // Check visibility
        if (!person.visibility || person.visibility === 'PUBLIC') {
          return true; // Public users are visible to everyone
        }

        if (person.visibility === 'PRIVATE') {
          // Private users are only visible to friends and themselves
          const personId = person.id?.toString();
          const isCurrentUser = personId === currentUserId.toString();
          const isUserFriend = personId && friendIds.includes(personId);

          const canView = isCurrentUser || isUserFriend;

          if (!canView) {
            console.log(
              '🔒 Filterng out private user (not friend):',
              person.fullName || person.name,
              'User  ID:',
              personId
            );
          }

          return canView;
        }

        // Default: if visibility is not specified, treat as public
        return true;
      });

      return filteredPeople;
    } catch (error) {
      console.error('❌ Error filtering people (showing all public users):', error);
      // On error, fall back to showing only public users
      return people.filter((person) => {
        const isPublic = !person.visibility || person.visibility === 'PUBLIC';
        return isPublic;
      });
    }
  }, []);

  // Fetch event categories from API
  const fetchCategories = useCallback(async () => {
    try {
      setIsLoadingCategories(true);
      setCategoriesError(null);
      const response = await EventService.getEventCategories();

      // Handle the specific API response structure: { body: [...], message: "...", success: true }
      let categoryNames: string[] = [];

      if (response && Array.isArray(response.body)) {
        // Main case: response.body contains the categories array
        categoryNames = response.body.map((cat: any) => cat.categoryName || cat.name || cat);
      } else if (Array.isArray(response.data)) {
        // Fallback: response.data contains categories
        categoryNames = response.data.map((cat: any) => cat.categoryName || cat.name || cat);
      } else if (Array.isArray(response.categories)) {
        // Fallback: response.categories contains categories
        categoryNames = response.categories.map((cat: any) => cat.categoryName || cat.name || cat);
      } else if (Array.isArray(response)) {
        // Fallback: direct array response
        categoryNames = response.map((cat: any) => cat.categoryName || cat.name || cat);
      } else {
        console.warn('Unexpected categories API response structure:', response);
        categoryNames = [];
      }

      // Ensure we always have 'All' as the first option
      const allCategories = ['All', ...categoryNames];
      setCategories(allCategories);
      return allCategories;
    } catch (error) {
      console.error('Error fetching categories:', error);
      setCategoriesError(error instanceof Error ? error.message : 'Failed to fetch categories');
      // Fallback to 'All' if API fails
      setCategories(['All']);
      return ['All'];
    } finally {
      setIsLoadingCategories(false);
    }
  }, []);

  // Fetch people from API
  const fetchPeople = useCallback(async () => {
    try {
      setIsLoadingPeople(true);
      setPeopleError(null);

      const response = await UserService.getUsers();

      // Get current user from store to exclude them
      const userStore = UserStore.getState() as { user: any };
      const currentUser = userStore.user;
      const currentUserId = currentUser?.id;

      // Handle the API response structure
      let users: Person[] = [];
      let rawUsers: any[] = [];

      if (response && Array.isArray(response.body)) {
        rawUsers = response.body;
      } else if (Array.isArray(response.data)) {
        rawUsers = response.data;
      } else if (Array.isArray(response)) {
        rawUsers = response;
      } else {
        console.warn('⚠️ Unexpected users API response structure:', response);
        rawUsers = [];
      }

      // Transform API users to Person format and filter out current user and users without location
      users = rawUsers
        .filter((user: any) => {
          // Exclude current user
          if (currentUserId && user.id === currentUserId) {
            return false;
          }

          // Only include users with valid location
          const hasValidLocation =
            user.location && Array.isArray(user.location) && user.location.length >= 2;
          if (!hasValidLocation) {
            return false;
          }

          // Filter out users who have been offline for more than 6 minutes
          if (user.loginTime) {
            const loginTime = new Date(user.loginTime);
            const currentTime = new Date();
            const timeDifferenceMinutes =
              (currentTime.getTime() - loginTime.getTime()) / (1000 * 60);

            if (timeDifferenceMinutes > 6) {
              return false;
            }
          }

          return true;
        })
        .map((user: any) => {
          const loginTime = new Date(user.loginTime);
          const currentTime = new Date();
          const timeDifferenceMinutes = (currentTime.getTime() - loginTime.getTime()) / (1000 * 60);

          // Get the profile picture from profilePicture array (use the latest one)
          let profilePhoto =
            'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png';
          if (
            user.profilePicture &&
            Array.isArray(user.profilePicture) &&
            user.profilePicture.length > 0
          ) {
            // Use the last profile picture in the array
            const latestPicture = user.profilePicture[user.profilePicture.length - 1];
            profilePhoto = latestPicture.secureUrl || latestPicture.url || profilePhoto;
          }

          return {
            id: user.id,
            name: user.fullName || 'Unknown User',
            age: user.age || 25, // Default age if not provided
            bio: user.bio || 'No bio available',
            profilePhoto,
            interests: user.interests || [],
            isOnline: timeDifferenceMinutes <= 6,
            lastActive: user.loginTime || new Date().toISOString(),
            upFor: user.upFor || '',
            location: user.location || '',
            // Use location coordinates from the location array
            long: user.location[0],
            lat: user.location[1],
            email: user.email,
            phoneNumber: user.phoneNumber || '', // Add phoneNumber with fallback
            // Include visibility for filtering (before transformation to Person type)
            visibility: user.visibility || 'PUBLIC', // Default to PUBLIC if not specified
            fullName: user.fullName, // Keep original fullName for filtering logs
          };
        });

      // Apply visibility filtering to users before setting them
      const filteredUsers = await filterPeople(users);

      // Clean up temporary fields and ensure proper Person type
      const cleanedUsers = filteredUsers.map((user: any) => ({
        id: user.id,
        name: user.name,
        age: user.age,
        bio: user.bio,
        profilePhoto: user.profilePhoto,
        interests: user.interests,
        isOnline: user.isOnline,
        lastActive: user.lastActive,
        upFor: user.upFor,
        location: user.location,
        long: user.long,
        lat: user.lat,
        email: user.email,
        phoneNumber: user.phoneNumber,
        // Keep visibility for potential future use
        visibility: user.visibility,
      }));

      setPeople(cleanedUsers);
      return cleanedUsers;
    } catch (error) {
      console.error('❌ Error fetching people:', error);
      setPeopleError(error instanceof Error ? error.message : 'Failed to fetch people');
      // Keep existing static data on error instead of clearing it
      console.log('🔄 Keeping static people data due to API error');
      return [];
    } finally {
      setIsLoadingPeople(false);
    }
  }, [filterPeople]);

  // Track last location update time and location values to avoid duplicates
  const lastLocationUpdateRef = useRef<{
    time: number;
    coords?: { latitude: number; longitude: number };
  }>({ time: 0 });

  const fetchAndUpdateLocation = useCallback(async () => {
    try {
      // Get current location
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      const { latitude, longitude } = location.coords;

      // Debounce logic - prevent multiple updates in quick succession (within 10 seconds)
      const now = Date.now();
      const lastUpdate = lastLocationUpdateRef.current;

      // If we've updated in the last 10 seconds and coordinates are very similar (within 10 meters), skip update
      const timeSinceLastUpdate = now - lastUpdate.time;
      const MIN_UPDATE_INTERVAL = 10 * 1000; // 10 seconds

      if (
        timeSinceLastUpdate < MIN_UPDATE_INTERVAL &&
        lastUpdate.coords &&
        Math.abs(lastUpdate.coords.latitude - latitude) < 0.0001 && // ~10m difference
        Math.abs(lastUpdate.coords.longitude - longitude) < 0.0001
      ) {
        console.log('📍 Skipping location update - too soon after previous update');
        return;
      }

      // Update reference with current time and coords
      lastLocationUpdateRef.current = {
        time: now,
        coords: { latitude, longitude },
      };

      // Update user profile with location
      const updatedUserData = {
        ...userData,
        location: [longitude, latitude], // MongoDB typically uses [longitude, latitude] format
      };

      const payload = {
        userId: updatedUserData.id,
        username: updatedUserData.username,
        phoneNumber: updatedUserData.phoneNumber,
        email: updatedUserData.email,
        fullName: updatedUserData.fullName,
        gender: updatedUserData.gender,
        interests: updatedUserData.interests,
        eventPreferences: updatedUserData.eventPreferences,
        location: updatedUserData.location,
        upFor: updatedUserData.upFor,
        bio: updatedUserData.bio,
        visibility: updatedUserData.visibility,
        loginTime: new Date().toISOString(),
      };
      console.log(payload);

      console.log('📍 Updating location in profile:', { latitude, longitude });
      const profileData = await UserService.updateProfile(payload);
      (UserStore.getState() as { setUser: (user: any) => void }).setUser(profileData.body);

      console.log('✅ Location updated successfully:', { latitude, longitude });
    } catch (error) {
      console.error('❌ Error fetching or updating location:', error);
    }
  }, [userData]);

  // Initialize data on component mount
  React.useEffect(() => {
    const initializeData = async () => {
      await Promise.all([
        fetchEvents(),
        fetchCategories(),
        fetchPeople(),
        fetchAndUpdateLocation(),
      ]);
    };

    initializeData();
  }, [fetchEvents, fetchCategories, fetchPeople, fetchAndUpdateLocation]);

  // Background data fetching with intervals and performance optimizations
  React.useEffect(() => {
    // Skip setting up intervals if continuous fetch is disabled
    if (!isContinuousFetchEnabled) {
      console.log('🚫 Continuous fetching disabled - skipping background intervals');
      return;
    }
    // Configuration for continuous data fetching while app is active
    // Optimized for real-time updates during active app usage
    const FETCH_INTERVALS = {
      events: 2 * 60 * 1000, // 2 minutes - slower for production to save data
      people: 1 * 60 * 1000, // 2 minutes - slower for production
      categories: 60 * 60 * 1000, // 1 hour - slower for production
      location: 2 * 60 * 1000, // 2 minutes - slower for production
    };

    // Store interval IDs and app state for cleanup and management
    const intervals: number[] = [];
    let isAppActive = true;

    // Track last fetch times to prevent duplicate requests
    const lastFetchTimes = {
      events: 0,
      people: 0,
      categories: 0,
      location: 0,
    };

    // Minimum time between fetches (10 seconds) for truly responsive updates
    const MIN_FETCH_INTERVAL = 10 * 1000;

    // App state change handler with immediate refresh when app becomes active
    const handleAppStateChange = (nextAppState: string) => {
      const wasActive = isAppActive;
      isAppActive = nextAppState === 'active';

      console.log(
        `📱 App state changed: ${nextAppState}, continuous fetching ${
          isAppActive ? 'enabled - keeping data fresh' : 'paused - saving resources'
        }`
      );

      // If app just became active after being inactive, immediately fetch fresh data
      if (isAppActive && !wasActive) {
        console.log('🔄 App became active - fetching fresh data immediately');
        setTimeout(() => {
          // Small delay to let the app fully resume
          fetchPeople().catch(console.error);
          setTimeout(() => fetchEvents().catch(console.error), 1000);
          setTimeout(() => fetchCategories().catch(console.error), 2000);
          setTimeout(() => fetchAndUpdateLocation().catch(console.error), 2000);
        }, 500);
      }
    };

    // Enhanced background fetch function with performance optimizations
    const createBackgroundFetch = (
      fetchFunction: () => Promise<any>,
      interval: number,
      name: string
    ) => {
      return setInterval(async () => {
        // Check if continuous fetching is still enabled
        if (!isContinuousFetchEnabled) {
          console.log(`🚫 Skipping ${name} fetch - continuous fetching disabled`);
          return;
        }

        // For continuous fetching while app is active, we only pause when app is inactive/background
        // This ensures data stays fresh during active usage
        if (!isAppActive) {
          console.log(`⏸️ Skipping ${name} fetch - app backgrounded (saving resources)`);
          return;
        }

        // Check if enough time has passed since last fetch (prevents excessive requests)
        const now = Date.now();
        const lastFetch = lastFetchTimes[name as keyof typeof lastFetchTimes];
        if (now - lastFetch < MIN_FETCH_INTERVAL) {
          console.log(
            `⏸️ Skipping ${name} fetch - respecting minimum interval (${MIN_FETCH_INTERVAL / 1000}s)`
          );
          return;
        }

        // Check network conditions before fetching
        if (!BackgroundFetchManager.isNetworkOptimal()) {
          console.log(`🌐 Skipping ${name} fetch - network conditions not optimal`);
          return;
        }

        try {
          console.log(`🔄 Continuous fetching ${name}... (keeping data fresh)`);
          lastFetchTimes[name as keyof typeof lastFetchTimes] = now;
          const startTime = Date.now();
          await fetchFunction();
          const duration = Date.now() - startTime;
          BackgroundFetchManager.recordFetch(name, true, duration);
          console.log(`✅ Continuous fetch ${name} completed in ${duration}ms - data refreshed`);
        } catch (error) {
          console.error(`❌ Continuous fetch ${name} failed:`, error);
          BackgroundFetchManager.recordFetch(name, false, 0);
          // Don't throw error to prevent breaking the app
        }
      }, interval);
    };

    // Set up background fetching intervals
    const eventsInterval = createBackgroundFetch(fetchEvents, FETCH_INTERVALS.events, 'events');
    const peopleInterval = createBackgroundFetch(fetchPeople, FETCH_INTERVALS.people, 'people');
    const categoriesInterval = createBackgroundFetch(
      fetchCategories,
      FETCH_INTERVALS.categories,
      'categories'
    );
    const locationInterval = createBackgroundFetch(
      fetchAndUpdateLocation,
      FETCH_INTERVALS.location,
      'location'
    );

    // Store intervals for cleanup
    intervals.push(eventsInterval, peopleInterval, categoriesInterval, locationInterval);

    // Immediate initial fetch to ensure fresh data when component mounts
    console.log('🚀 Starting continuous data fetching - initial load');
    Promise.all([
      fetchPeople().catch(console.error),
      fetchEvents().catch(console.error),
      fetchCategories().catch(console.error),
      fetchAndUpdateLocation().catch(console.error),
    ]).then(() => {
      console.log('✅ Initial data fetch completed - continuous fetching active');
    });

    // Setup app state listener (if available)
    let appStateSubscription: any = null;
    if (AppState) {
      appStateSubscription = AppState.addEventListener('change', handleAppStateChange);
    }

    // Cleanup function to clear all intervals and listeners
    return () => {
      console.log('🧹 Cleaning up background fetch intervals');
      intervals.forEach(clearInterval);

      // Remove app state listener
      if (appStateSubscription) {
        appStateSubscription.remove();
      }
    };
  }, [fetchEvents, fetchPeople, fetchCategories, isContinuousFetchEnabled]);

  // Memoize callback functions to prevent unnecessary rerenders
  const zoomToLocation = useCallback((coordinates: coodinates, zoomLevel = 18, duration = 1000) => {
    setFollowUserLocation(false);
    if (cameraRef.current && cameraRef.current.setCamera) {
      cameraRef.current.setCamera({
        centerCoordinate: [coordinates.longitude, coordinates.latitude],
        zoomLevel,
        animationDuration: duration,
      });
    }
  }, []);

  const resetToUserLocation = useCallback(() => {
    setFollowUserLocation(true);
  }, []);

  // Memoize state setters
  const memoizedSetSelectedEvent = useCallback((event: EventType | null) => {
    setSelectedEvent(event);
  }, []);

  const memoizedSetSearchQuery = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const memoizedSetViewMode = useCallback((mode: ViewModeType) => {
    setViewMode(mode);
  }, []);

  const memoizedSetMapType = useCallback((type: MapTsType) => {
    setMapType(type);
  }, []);

  const memoizedSetFollowUserLocation = useCallback((value: boolean) => {
    setFollowUserLocation(value);
  }, []);

  const memoizedSetZoomLevel = useCallback((zoom: number) => {
    // Round zoom level to reduce unnecessary updates
    const roundedZoom = Math.round(zoom * 10) / 10;
    setZoomLevel(roundedZoom);
  }, []);

  const memoizedSetUserId = useCallback((id: string | number | null) => {
    setUserId(id);
  }, []);

  const memoizedSetPeople = useCallback((newPeople: Person[]) => {
    setPeople(newPeople);
  }, []);

  const memoizedSetIsPersonProfileSheetOpen = useCallback((isOpen: boolean) => {
    setIsPersonProfileSheetOpen(isOpen);
  }, []);

  const memoizedSetIsContinuousFetchEnabled = useCallback((enabled: boolean) => {
    setIsContinuousFetchEnabled(enabled);
    console.log(`🔄 Continuous fetching ${enabled ? 'enabled' : 'disabled'}`);
  }, []);

  React.useEffect(() => {
    if (searchQuery === 'All') {
      setFilteredEvents(allEvents);
      resetToUserLocation();
    } else {
      const filtered = allEvents.filter((event) => event.eventType === searchQuery);

      setFilteredEvents(filtered);
      setTimeout(() => {
        if (ViewMode === ViewModeMap.ScrollView && filtered.length > 0) {
          const coords = {
            latitude: filtered[0].locationData.coordinates[1],
            longitude: filtered[0].locationData.coordinates[0],
          };
          zoomToLocation(coords);
        }
      }, 500);
    }
  }, [searchQuery, ViewMode, allEvents, zoomToLocation, resetToUserLocation]);

  // Memoize the context value to prevent unnecessary rerenders
  const contextValue = useMemo(
    () => ({
      selectedEvent,
      setSelectedEvent: memoizedSetSelectedEvent,
      setSearchQuery: memoizedSetSearchQuery,
      filteredEvents,
      categories,
      searchQuery,
      cameraRef,
      zoomToLocation,
      resetToUserLocation,
      followUserLocation,
      setFollowUserLocation: memoizedSetFollowUserLocation,
      ViewMode,
      setViewMode: memoizedSetViewMode,
      MapType,
      setMapType: memoizedSetMapType,
      People,
      zoomLevel,
      setZoomLevel: memoizedSetZoomLevel,
      userId,
      setUserId: memoizedSetUserId,
      personProfileSheetRef,
      setPeople: memoizedSetPeople,
      isPersonProfileSheetOpen,
      setIsPersonProfileSheetOpen: memoizedSetIsPersonProfileSheetOpen,
      // Add new API-related values
      isLoadingEvents,
      isLoadingCategories,
      isLoadingPeople,
      eventsError,
      categoriesError,
      peopleError,
      fetchEvents,
      fetchCategories,
      fetchPeople,
      isContinuousFetchEnabled,
      setIsContinuousFetchEnabled: memoizedSetIsContinuousFetchEnabled,
    }),
    [
      selectedEvent,
      filteredEvents,
      categories,
      searchQuery,
      followUserLocation,
      ViewMode,
      MapType,
      People,
      zoomLevel,
      userId,
      isPersonProfileSheetOpen,
      isLoadingEvents,
      isLoadingCategories,
      isLoadingPeople,
      eventsError,
      categoriesError,
      peopleError,
      isContinuousFetchEnabled,
      // Remove memoized functions from dependencies since they're stable with useCallback
      // memoizedSetSelectedEvent,
      // memoizedSetSearchQuery,
      // zoomToLocation,
      // resetToUserLocation,
      // memoizedSetFollowUserLocation,
      // memoizedSetViewMode,
      // memoizedSetMapType,
      // memoizedSetZoomLevel,
      // memoizedSetUserId,
      // memoizedSetPeople,
      // memoizedSetIsPersonProfileSheetOpen,
      // fetchEvents,
      // fetchCategories,
      // fetchPeople,
    ]
  );

  return (
    <EventProviderContext.Provider value={contextValue}>{children}</EventProviderContext.Provider>
  );
};

export const useEvent = () => useContext(EventProviderContext);

// Performance monitoring and optimization utilities
const BackgroundFetchManager = {
  // Track fetch performance metrics
  metrics: {
    events: { successCount: 0, errorCount: 0, avgDuration: 0 },
    people: { successCount: 0, errorCount: 0, avgDuration: 0 },
    categories: { successCount: 0, errorCount: 0, avgDuration: 0 },
  },

  // Dynamic interval adjustment based on success/failure rates
  getOptimizedInterval: (baseInterval: number, type: string) => {
    const metric =
      BackgroundFetchManager.metrics[type as keyof typeof BackgroundFetchManager.metrics];
    if (!metric) return baseInterval;

    const successRate = metric.successCount / (metric.successCount + metric.errorCount || 1);

    // Increase interval if success rate is low (network issues)
    if (successRate < 0.7) {
      return Math.min(baseInterval * 2, 30 * 60 * 1000); // Max 30 minutes
    }

    // Decrease interval if success rate is high and data is fresh
    if (successRate > 0.95 && metric.avgDuration < 1000) {
      return Math.max(baseInterval * 0.8, 60 * 1000); // Min 1 minute
    }

    return baseInterval;
  },

  // Record fetch metrics
  recordFetch: (type: string, success: boolean, duration: number) => {
    const metric =
      BackgroundFetchManager.metrics[type as keyof typeof BackgroundFetchManager.metrics];
    if (!metric) return;

    if (success) {
      metric.successCount++;
      // Calculate rolling average
      metric.avgDuration = (metric.avgDuration + duration) / 2;
    } else {
      metric.errorCount++;
    }
  },

  // Check if device has good network conditions
  isNetworkOptimal: () => {
    // This could be enhanced with network info from react-native-netinfo
    // For now, return true - in production, add network condition checks
    return true;
  },
};
