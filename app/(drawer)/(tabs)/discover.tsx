import { MaterialIcons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, TextInput, ScrollView, Image } from 'react-native';
import { Toast } from 'toastify-react-native';

import EventMarkerSheet from '~/components/Event/EventMarkerSheet';
import { useColorScheme } from '~/lib/useColorScheme';
import { calculateTicketStats } from '~/lib/eventStatusUtils';
import { EventProvider, useEvent } from '~/providers/MapProvider';
import { EventService } from '~/services/EventService';
import { UserStore } from '~/store/store';
import { EventType } from '~/types';

// Event Card Component
const EventCard = React.memo(
  ({
    item,
    onPress,
    colors,
    savedEvents,
    onSaveEvent,
  }: {
    item: EventType;
    onPress: (item: EventType) => void;
    colors: any;
    savedEvents: string[];
    onSaveEvent: (eventId: string, isSaved: boolean) => void;
  }) => {
    // Format time
    const formatTime = (dateString: string) => {
      const date = new Date(dateString);
      return format(date, 'h:mm a');
    };

    // Format date for display in the new design
    const formatDisplayDate = (dateString: string) => {
      const date = new Date(dateString);
      return `${format(date, 'EEE')}, ${format(date, 'MMM d')}`;
    };

    // Get event image or a placeholder based on event type
    const getEventImage = () => {
      if (item.coverImage) return item.coverImage;

      // Default placeholder images based on event type
      const placeholders: Record<string, string> = {
        Business:
          'https://images.unsplash.com/photo-1565398305935-49e5dcc5c9f6?q=80&w=1920&auto=format',
        Leisure:
          'https://images.unsplash.com/photo-1517457373958-b7bdd4587205?q=80&w=1920&auto=format',
        Entertainment:
          'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?q=80&w=1920&auto=format',
        Education:
          'https://images.unsplash.com/photo-1524178232363-1fb2b075b655?q=80&w=1920&auto=format',
      };

      return (
        placeholders[item.eventType] ||
        'https://images.unsplash.com/photo-1540575467063-178a50c2df87?q=80&w=1920&auto=format'
      );
    };

    const isSaved = savedEvents.includes(item.id.toString());

    const handleSavePress = (e: any) => {
      e.stopPropagation(); // Prevent triggering the card press
      onSaveEvent(item.id.toString(), isSaved);
    };

    // Check if event has expired
    const isEventExpired = () => {
      const now = new Date();
      const eventEndDate = new Date(item.endDateTime || item.startDateTime);
      return eventEndDate < now;
    };

    return (
      <TouchableOpacity
        onPress={() => onPress(item)}
        activeOpacity={0.7}
        className="mx-4 mb-4 overflow-hidden rounded-xl border"
        style={{
          backgroundColor: colors.background,
          borderColor: colors.grey5,
          opacity: isEventExpired() ? 0.7 : 1,
        }}>
        {/* Event Image - Always show an image (either the event's cover or a placeholder) */}
        <View className="relative h-32 w-full">
          <Image source={{ uri: getEventImage() }} className="h-full w-full" resizeMode="cover" />

          {/* Paid/Free Tag */}
          <View className="absolute left-2 top-2 flex-row gap-2">
            <View
              className="rounded-full px-2 py-1"
              style={{
                backgroundColor: item.isPaid ? colors.primary : '#22c55e',
              }}>
              <Text className="font-bold text-xs text-white">{item.isPaid ? 'Paid' : 'Free'}</Text>
            </View>

            {/* Expired Tag */}
            {isEventExpired() && (
              <View
                className="rounded-full px-2 py-1"
                style={{
                  backgroundColor: '#ef4444',
                }}>
                <Text className="font-bold text-xs text-white">Expired</Text>
              </View>
            )}
          </View>
        </View>

        {/* Event Details */}
        <View className="p-3">
          {/* Date and Time */}
          <View className="flex flex-row items-center justify-between">
            <Text className="font-medium text-sm" style={{ color: colors.grey }}>
              {formatDisplayDate(item.startDateTime)} • {formatTime(item.startDateTime)}
            </Text>
            <TouchableOpacity className="p-2" onPress={handleSavePress}>
              <MaterialIcons
                name={isSaved ? 'favorite' : 'favorite-border'}
                size={22}
                color={isSaved ? colors.primary : colors.grey}
              />
            </TouchableOpacity>
          </View>

          {/* Title */}
          <Text
            style={{ color: colors.foreground }}
            className="font-bold text-lg"
            numberOfLines={2}>
            {item.title}
          </Text>

          {/* Location */}
          <View className="mb-3 mt-1">
            <Text style={{ color: colors.grey }} className="text-sm" numberOfLines={1}>
              {item.location}
            </Text>
          </View>

          {/* Attending Count */}
          <View className="mb-3 flex-row items-center">
            <MaterialIcons name="people" size={16} color={colors.grey} />
            <Text className="ml-1 text-sm" style={{ color: colors.grey }}>
              {calculateTicketStats(item).attendees} attending
            </Text>
          </View>

          {/* Event Owner */}
          <View className="mb-3 flex-row items-center">
            <Image
              source={{
                uri:
                  item.user?.profilePicture &&
                  Array.isArray(item.user.profilePicture) &&
                  item.user.profilePicture.length > 0 &&
                  item.user.profilePicture[item.user.profilePicture.length - 1]?.secureUrl
                    ? item.user.profilePicture[item.user.profilePicture.length - 1].secureUrl
                    : 'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
              }}
              className="mr-2 h-6 w-6 rounded-full"
              style={{ resizeMode: 'cover' }}
            />
            <Text
              className="flex-1 font-medium text-sm"
              style={{ color: colors.foreground }}
              numberOfLines={1}>
              {item.user?.fullName || 'Unknown Host'}
            </Text>

            {/* Rating */}
            {/*   <View className="flex-row items-center">
              <Ionicons name="star" size={12} color="#FFD700" />
              <Text className="ml-1 text-xs" style={{ color: colors.grey }}>
                {generateDemoRating()}
              </Text>
            </View> */}
          </View>
        </View>
      </TouchableOpacity>
    );
  }
);

function DiscoverComponent() {
  const { colors, isDark } = useColorScheme();
  const { filteredEvents, setSearchQuery, setSelectedEvent, categories } = useEvent();
  const initialUserData = UserStore((state: any) => state.user);

  // State for filters
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [timeFilter, setTimeFilter] = useState('Anytime');
  const [priceFilter, setPriceFilter] = useState('All'); // "All", "Free", "Paid"
  const [suggestedEvents, setSuggestedEvents] = useState<EventType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [savedEvents, setSavedEvents] = useState<string[]>([]);

  // Time filter options
  const timeFilterOptions = ['Anytime', 'Today', 'This Week', 'This Month', 'This Weekend'];

  // Function to check if event matches time filter
  const matchesTimeFilter = useCallback((event: EventType, filter: string) => {
    if (filter === 'Anytime') return true;

    const eventDate = new Date(event.startDateTime);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    switch (filter) {
      case 'Today': {
        const eventDateOnly = new Date(
          eventDate.getFullYear(),
          eventDate.getMonth(),
          eventDate.getDate()
        );
        return eventDateOnly.getTime() === today.getTime();
      }

      case 'This Week': {
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);
        return eventDate >= weekStart && eventDate <= weekEnd;
      }

      case 'This Month': {
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        monthEnd.setHours(23, 59, 59, 999);
        return eventDate >= monthStart && eventDate <= monthEnd;
      }

      case 'This Weekend': {
        const saturday = new Date(today);
        saturday.setDate(today.getDate() + (6 - today.getDay()));
        const sunday = new Date(saturday);
        sunday.setDate(saturday.getDate() + 1);
        sunday.setHours(23, 59, 59, 999);
        return eventDate >= saturday && eventDate <= sunday;
      }

      default:
        return true;
    }
  }, []);

  // Fetch event suggestions
  const fetchEventSuggestions = useCallback(async () => {
    if (!initialUserData?.id) return;

    setIsLoading(true);
    setError(null);
    try {
      const response = await EventService.getEventSuggestions(initialUserData.id);
      setSuggestedEvents(response.data || []);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch event suggestions');
      console.error('Error fetching event suggestions:', err);
    } finally {
      setIsLoading(false);
    }
  }, [initialUserData?.id]);

  // Fetch saved events for the user
  const fetchSavedEvents = useCallback(async () => {
    if (!initialUserData?.id) return;

    try {
      const response = await EventService.getSavedEvents(initialUserData.id);
      if (response.success && response.body) {
        const eventIds = response.body.events.map(
          (event: any) => event.id || event.eventId || event._id
        );
        setSavedEvents(eventIds);
      }
    } catch (error) {
      console.error('Error fetching saved events:', error);
    }
  }, [initialUserData?.id]);

  // Handle save/unsave event
  const handleSaveEvent = useCallback(
    async (eventId: string, isSaved: boolean) => {
      if (!initialUserData?.id) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Please login to save events',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
        });
        return;
      }

      try {
        await EventService.saveEvent(initialUserData.id, eventId);

        // Update local saved events state
        if (isSaved) {
          // Remove from saved events
          setSavedEvents((prev) => prev.filter((id) => id !== eventId));
        } else {
          // Add to saved events
          setSavedEvents((prev) => [...prev, eventId]);
        }

        /*  Toast.show({
          type: 'success',
          text1: isSaved ? 'Event Unsaved' : 'Event Saved',
          text2: isSaved
            ? 'Event removed from your saved events'
            : 'Event added to your saved events',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
        }); */
      } catch (error: any) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: error.message || 'Failed to save event. Please try again.',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
        });
      } finally {
        // Removed setIsSavingEvent call since it's not used for UI feedback
      }
    },
    [initialUserData?.id, isDark]
  );

  // Filter the events based on all filters
  const getFilteredEvents = useCallback(() => {
    // Use suggested events if available, otherwise fall back to filteredEvents
    const eventsToFilter = suggestedEvents.length > 0 ? suggestedEvents : filteredEvents;
    let filtered = [...eventsToFilter];

    // Filter by price
    if (priceFilter === 'Free') {
      filtered = filtered.filter((event) => !event.isPaid);
    } else if (priceFilter === 'Paid') {
      filtered = filtered.filter((event) => event.isPaid);
    }

    // Filter by time
    if (timeFilter !== 'Anytime') {
      filtered = filtered.filter((event) => matchesTimeFilter(event, timeFilter));
    }

    // Filter by search text
    if (searchText) {
      const query = searchText.toLowerCase();
      filtered = filtered.filter(
        (event) =>
          event.title.toLowerCase().includes(query) ||
          event.description.toLowerCase().includes(query) ||
          event.location.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [suggestedEvents, filteredEvents, priceFilter, timeFilter, searchText, matchesTimeFilter]);

  // Apply category filter through the MapProvider
  const handleCategorySelect = useCallback(
    (category: string) => {
      setSelectedCategory(category);
      setSearchQuery(category);
    },
    [setSearchQuery]
  );

  // Handle event selection
  const handleSelectEvent = useCallback(
    (event: EventType) => {
      setSelectedEvent(event);
    },
    [setSelectedEvent]
  );

  // Render event item
  const renderEventItem = useCallback(
    ({ item }: { item: EventType }) => {
      return (
        <EventCard
          item={item}
          onPress={handleSelectEvent}
          colors={colors}
          savedEvents={savedEvents}
          onSaveEvent={handleSaveEvent}
        />
      );
    },
    [colors, handleSelectEvent, savedEvents, handleSaveEvent]
  );

  // Make sure we always have events when the component mounts
  useEffect(() => {
    // Reset category filter to show all events
    if (selectedCategory !== 'All') {
      setSearchQuery('All');
      setSelectedCategory('All');
    }

    // Fetch event suggestions and saved events
    fetchEventSuggestions();
    fetchSavedEvents();
  }, [fetchEventSuggestions, fetchSavedEvents]);

  // Get the final filtered events
  const displayEvents = getFilteredEvents();

  return (
    <>
      <View className="flex-1 pt-12" style={{ backgroundColor: colors.background }}>
        <StatusBar style={isDark ? 'light' : 'dark'} />

        {/* Header Title */}
        <View className="flex-row items-center justify-center px-4 pb-4">
          <Text className={`font-medium text-xl ${isDark ? 'text-white' : 'text-black'}`}>
            Discover Events
          </Text>
        </View>

        {/* Search Bar */}
        <View className="mx-4 mb-4 mt-4">
          <View
            className="flex-row items-center rounded-lg px-3 py-0"
            style={{ backgroundColor: colors.grey5 }}>
            <MaterialIcons name="search" size={22} color={colors.grey} />
            <TextInput
              placeholder="Search for events..."
              placeholderTextColor={colors.grey}
              value={searchText}
              onChangeText={setSearchText}
              className="ml-2 h-10 flex-1 text-base"
              style={{ color: colors.foreground }}
            />
            {searchText ? (
              <TouchableOpacity onPress={() => setSearchText('')}>
                <MaterialIcons name="close" size={22} color={colors.grey} />
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        {/* Filter Buttons */}
        <View className="mb-4 flex-row px-4">
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {/* Categories */}
            {[...categories].map((category) => (
              <TouchableOpacity
                key={category}
                className="mr-3 flex-row items-center rounded-lg px-3 py-2"
                style={{
                  backgroundColor: selectedCategory === category ? colors.primary : colors.grey5,
                }}
                onPress={() => handleCategorySelect(category)}>
                <Text
                  className="font-medium"
                  style={{
                    color: selectedCategory === category ? '#fff' : colors.grey,
                  }}>
                  {category.charAt(0).toUpperCase() + category.slice(1).toLowerCase()}
                </Text>
              </TouchableOpacity>
            ))}

            {/* Price Filter */}
            <TouchableOpacity
              className="mr-3 flex-row items-center rounded-lg px-3 py-2"
              style={{
                backgroundColor: priceFilter !== 'All' ? colors.primary : colors.grey5,
              }}
              onPress={() => {
                // Toggle between All, Free, Paid
                if (priceFilter === 'All') setPriceFilter('Free');
                else if (priceFilter === 'Free') setPriceFilter('Paid');
                else setPriceFilter('All');
              }}>
              <Text
                className="font-medium"
                style={{
                  color: priceFilter !== 'All' ? '#fff' : colors.grey,
                }}>
                {priceFilter === 'All' ? 'Price' : priceFilter}
              </Text>
            </TouchableOpacity>

            {/* Time Filter */}
            <TouchableOpacity
              className="mr-3 flex-row items-center rounded-lg px-3 py-2"
              style={{
                backgroundColor: timeFilter !== 'Anytime' ? colors.primary : colors.grey5,
              }}
              onPress={() => {
                // Cycle through time filter options
                const currentIndex = timeFilterOptions.indexOf(timeFilter);
                const nextIndex = (currentIndex + 1) % timeFilterOptions.length;
                setTimeFilter(timeFilterOptions[nextIndex]);
              }}>
              <MaterialIcons
                name="access-time"
                size={18}
                color={timeFilter !== 'Anytime' ? '#fff' : colors.grey}
              />
              <Text
                className="ml-1 font-medium"
                style={{
                  color: timeFilter !== 'Anytime' ? '#fff' : colors.grey,
                }}>
                {timeFilter}
              </Text>
              <MaterialIcons
                name="keyboard-arrow-down"
                size={18}
                color={timeFilter !== 'Anytime' ? '#fff' : colors.grey}
              />
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Events Count and Sort */}
        <View className="mb-2 flex-row items-center justify-between px-4">
          <Text className="font-medium" style={{ color: colors.foreground }}>
            {isLoading ? 'Loading...' : `${displayEvents.length} events`}
          </Text>
        </View>

        {/* Events List */}
        <FlatList
          data={displayEvents}
          renderItem={renderEventItem}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingVertical: 10 }}
          refreshing={isLoading}
          onRefresh={fetchEventSuggestions}
          ListEmptyComponent={
            <View className="flex-1 items-center justify-center py-10">
              <MaterialIcons name="event-busy" size={60} color={colors.grey5} />
              <Text className="mt-4 text-center" style={{ color: colors.grey }}>
                {error ? error : 'No events found'}
              </Text>
              <TouchableOpacity
                className="mt-4 rounded-lg px-4 py-2"
                style={{ backgroundColor: colors.primary }}
                onPress={() => {
                  setSearchText('');
                  setPriceFilter('All');
                  setSelectedCategory('All');
                  setTimeFilter('Anytime');
                  setSearchQuery('All');
                  if (error) {
                    setError(null);
                    fetchEventSuggestions();
                  }
                }}>
                <Text className="font-medium text-white">
                  {error ? 'Retry' : 'Clear all filters'}
                </Text>
              </TouchableOpacity>
            </View>
          }
        />

        {/* Event Marker Sheet will be rendered by the EventMarkerSheet component */}
      </View>
      <EventMarkerSheet />
    </>
  );
}
export default function Discover() {
  return (
    <EventProvider>
      <DiscoverComponent />
    </EventProvider>
  );
}
