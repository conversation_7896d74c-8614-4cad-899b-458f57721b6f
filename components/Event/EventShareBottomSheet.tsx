import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  TextInput,
  Image,
} from 'react-native';
import { BottomSheet, BottomSheetView } from '@gorhom/bottom-sheet';
import { Ionicons } from '@expo/vector-icons';
import { Toast } from 'toastify-react-native';

import { useColorScheme } from '~/lib/useColorScheme';
import { FriendService } from '~/services/FriendService';
import { UserStore } from '~/store/store';
import { Friend } from '~/types/chat_type';
import { EventType } from '~/types/event_type';

interface EventShareBottomSheetProps {
  bottomSheetRef: React.RefObject<BottomSheet | null>;
  event: EventType | null;
  onSelectFriend: (friend: Friend, event: EventType) => void;
  colors: any;
  isDark: boolean;
  isOpen?: boolean;
  onClose?: () => void;
}

const EventShareBottomSheet = ({
  bottomSheetRef,
  event,
  onSelectFriend,
  colors,
  isDark,
  isOpen = false,
  onClose,
}: EventShareBottomSheetProps) => {
  const user = UserStore((state: any) => state.user);
  const [searchQuery, setSearchQuery] = useState('');
  const [friends, setFriends] = useState<Friend[]>([]);
  const [filteredFriends, setFilteredFriends] = useState<Friend[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch friends when sheet opens
  useEffect(() => {
    if (isOpen && user?.id) {
      fetchFriends();
    }
  }, [isOpen, user?.id]);

  // Filter friends based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredFriends(friends);
    } else {
      const filtered = friends.filter((friend) =>
        friend.fullName?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredFriends(filtered);
    }
  }, [searchQuery, friends]);

  const fetchFriends = async () => {
    try {
      setLoading(true);
      const response = await FriendService.getFriends(user.id);
      if (response.success) {
        setFriends(response.body.friends || []);
      } else {
        Toast.error('Failed to load friends');
      }
    } catch (error) {
      console.error('Error fetching friends:', error);
      Toast.error('Failed to load friends');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectFriend = (friend: Friend) => {
    if (event) {
      onSelectFriend(friend, event);
      setSearchQuery('');
      onClose?.();
    }
  };

  const renderFriendItem = ({ item }: { item: Friend }) => (
    <TouchableOpacity
      className="flex-row items-center border-b p-4"
      style={{ borderBottomColor: colors.grey5 }}
      onPress={() => handleSelectFriend(item)}>
      <Image
        source={{
          uri:
            item.profilePicture?.[item.profilePicture.length - 1]?.secureUrl ||
            item.profilePhoto ||
            'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
        }}
        className="mr-3 h-12 w-12 rounded-full"
        style={{ backgroundColor: colors.grey5 }}
      />
      <View className="flex-1">
        <Text className="font-medium text-base" style={{ color: colors.foreground }}>
          {item.fullName}
        </Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color={colors.foreground} />
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View className="flex-1 items-center justify-center py-8">
      <Ionicons
        name="people-outline"
        size={48}
        color={colors.foreground}
        style={{ opacity: 0.5 }}
      />
      <Text className="mt-4 font-medium text-lg" style={{ color: colors.foreground }}>
        {searchQuery ? 'No friends found' : 'No friends yet'}
      </Text>
      <Text
        className="mt-2 px-8 text-center text-sm opacity-70"
        style={{ color: colors.foreground }}>
        {searchQuery
          ? 'Try adjusting your search'
          : 'Add friends to start sharing events with them'}
      </Text>
    </View>
  );

  return (
    <BottomSheetView className="flex-1 px-4 pt-2">
      {/* Header */}
      <View className="mb-4 flex-row items-center justify-between">
        <Text className="text-lg font-semibold" style={{ color: colors.foreground }}>
          Share Event
        </Text>
        <TouchableOpacity onPress={onClose} className="p-2">
          <Ionicons name="close" size={24} color={colors.foreground} />
        </TouchableOpacity>
      </View>

      {/* Event Preview */}
      {event && (
        <View className="mb-4 rounded-lg border p-3" style={{ borderColor: colors.grey5 }}>
          <Text
            className="mb-1 font-medium text-sm opacity-70"
            style={{ color: colors.foreground }}>
            Sharing:
          </Text>
          <Text className="text-base font-semibold" style={{ color: colors.foreground }}>
            {event.title}
          </Text>
        </View>
      )}

      {/* Search Input */}
      <View className="mb-4">
        <View
          className="flex-row items-center rounded-lg px-3 py-2"
          style={{ backgroundColor: colors.grey6 }}>
          <Ionicons name="search" size={20} color={colors.foreground} style={{ opacity: 0.5 }} />
          <TextInput
            className="ml-2 flex-1 text-base"
            style={{ color: colors.foreground, fontFamily: 'Regular' }}
            placeholder="Search friends..."
            placeholderTextColor={`${colors.foreground}80`}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      {/* Friends List */}
      {loading ? (
        <View className="flex-1 items-center justify-center">
          <ActivityIndicator size="large" color={colors.primary} />
          <Text className="mt-4 text-base opacity-70" style={{ color: colors.foreground }}>
            Loading friends...
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredFriends}
          renderItem={renderFriendItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyState}
          contentContainerStyle={{ flexGrow: 1 }}
        />
      )}
    </BottomSheetView>
  );
};

export default EventShareBottomSheet;
