import { Ionicons, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { format } from 'date-fns';
import { useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  Linking,
  Platform,
  BackHandler,
  TextInput,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Toast } from 'toastify-react-native';

import LocationPreview from '../Map/LocationPreview';
import PersonProfileSheet, { PersonProfileSheetHandle } from '../People/PersonProfileSheet';
import PhotoViewModal from '../Profile/PhotoViewModal';
import { RenderBackdrop } from '../RenderBackdrop';
import EventShareBottomSheet from './EventShareBottomSheet';

import { useColorScheme } from '~/lib/useColorScheme';
import { getEventStatus, calculateTicketStats } from '~/lib/eventStatusUtils';
import { useEvent } from '~/providers/MapProvider';
import { EventService } from '~/services/EventService';
import { PaymentPollingService } from '~/services/PaymentPollingService';
import { UserStore } from '~/store/store';
import { EventType } from '~/types';
import { Friend } from '~/types/chat_type';
import { useFirebaseChat } from '~/hooks/useFirebaseChat';
import PaymentResultBottomSheet, {
  PaymentResultBottomSheetHandle,
} from '../Payment/PaymentResultBottomSheet';
import PaymentWebViewBottomSheet, {
  PaymentWebViewBottomSheetHandle,
} from '../Payment/PaymentWebViewBottomSheet';

// Ticket Info Component
const TicketsContent: React.FC<{
  event: EventType;
  onBack: () => void;
}> = ({ event, onBack }) => {
  const { colors } = useColorScheme();
  const router = useRouter();
  const [selectedTicketType, setSelectedTicketType] = useState<string | null>(null);
  const [ticketQuantity, setTicketQuantity] = useState<string>('1');
  const [isProcessing, setIsProcessing] = useState(false);
  const [registrationStatus, setRegistrationStatus] = useState<'success' | 'failed' | null>(null);
  const [registrationMessage, setRegistrationMessage] = useState('');
  const currentUser = UserStore((state: any) => state.user);
  const paymentResultSheetRef = useRef<PaymentResultBottomSheetHandle>(null);
  const paymentWebViewSheetRef = useRef<PaymentWebViewBottomSheetHandle>(null);
  const pollingRef = useRef<{ cancel: () => void; pollId: string } | null>(null);
  const [isWebViewOpen, setIsWebViewOpen] = useState(false);

  // Handle webview close
  const handleWebViewClose = () => {
    if (isWebViewOpen && pollingRef.current) {
      console.log('WebView closed, stopping polling');
      pollingRef.current.cancel();
      pollingRef.current = null;
      setIsWebViewOpen(false);
    }
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return format(date, 'EEEE, MMMM d');
  };

  const formatTime = (dateString: string | undefined) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return format(date, 'h:mm a');
  };

  const getTicketInfo = () => {
    if (!event) return null;

    if (!event.isPaid) {
      return {
        isFree: true,
        price: 'Free',
        totalTickets: event.ticketSetup.totalTickets,
      };
    }

    if (event.ticketSetup.hasLevels) {
      return {
        isFree: false,
        hasLevels: true,
        levels: event.ticketSetup.levels,
      };
    }

    return {
      isFree: false,
      hasLevels: false,
      price: event.ticketSetup.price,
      totalTickets: event.ticketSetup.totalTickets,
    };
  };

  const ticketInfo = getTicketInfo() || { isFree: true, price: 0, totalTickets: 0 };

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      if (pollingRef.current) {
        pollingRef.current.cancel();
      }
    };
  }, []);

  const handleTicketPurchase = async (ticketType: string, price: number) => {
    if (!currentUser?.id) {
      Toast.show({
        type: 'error',
        text1: 'Authentication Required',
        text2: 'Please log in to purchase tickets',
        position: 'bottom',
      });
      return;
    }

    const quantity = parseInt(ticketQuantity, 10);
    if (isNaN(quantity) || quantity < 1) {
      Toast.show({
        type: 'error',
        text1: 'Invalid Quantity',
        text2: 'Please enter a valid ticket quantity',
        position: 'bottom',
      });
      return;
    }

    setIsProcessing(true);
    try {
      console.log(event.id.toString(), ticketType, price, quantity);
      const response = await EventService.registerForEvent({
        eventId: event.id.toString(),
        ticketType,
        price,
        quantity,
      });

      if (response.success && response.body?.redirectUrl) {
        // Cancel any existing polling operations before navigating
        PaymentPollingService.cancelAllPolling();
        console.log('Cancelled existing polls before navigating to payment');

        // Navigate to payment page
        router.push({
          pathname: '/(drawer)/payment',
          params: {
            paymentUrl: response.body.redirectUrl,
            pollUrl: response.body.pollUrl,
            eventTitle: event.title,
            ticketType,
            quantity: quantity.toString(),
            from: 'eventsheet',
          },
        });
      } else if (response.success && !response.body?.redirectUrl) {
        // Payment URL is missing - show error and don't navigate
        throw new Error('There was an issue trying to initiate payment. Please try again later.');
      } else {
        throw new Error(response.message || 'Failed to initiate payment');
      }
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Purchase Failed',
        text2: error.message || 'Failed to purchase tickets. Please try again.',
        position: 'bottom',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFreeRegistration = async () => {
    if (!currentUser?.id) {
      Toast.show({
        type: 'error',
        text1: 'Authentication Required',
        text2: 'Please log in to register for this event',
        position: 'bottom',
      });
      return;
    }

    const quantity = parseInt(ticketQuantity, 10);
    if (isNaN(quantity) || quantity < 1) {
      Toast.show({
        type: 'error',
        text1: 'Invalid Quantity',
        text2: 'Please enter a valid number of attendees',
        position: 'bottom',
      });
      return;
    }

    setIsProcessing(true);
    try {
      await EventService.registerToAttendEvent({
        eventId: event.id.toString(),
        ticketType: 'Free',
        price: 0,
        quantity,
      });

      setRegistrationStatus('success');
      setRegistrationMessage(
        `Successfully registered ${quantity} attendee${quantity > 1 ? 's' : ''} for ${event.title}`
      );
    } catch {
      setRegistrationStatus('failed');
      setRegistrationMessage('Failed to register for event. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleViewTickets = () => {
    router.replace('/(drawer)/tickets');
  };

  const handleRetryRegistration = () => {
    setRegistrationStatus(null);
    setRegistrationMessage('');
  };

  const handleCloseRegistrationStatus = () => {
    setRegistrationStatus(null);
    setRegistrationMessage('');
    onBack();
  };

  // Show success/failure overlay
  if (registrationStatus === 'success' || registrationStatus === 'failed') {
    return (
      <View style={{ flex: 1, backgroundColor: colors.background }}>
        {/* Header */}
        <View
          className="flex-row items-center justify-between border-b px-4 py-3"
          style={{
            borderBottomColor: colors.grey5,
            paddingTop: 12,
          }}>
          <Text className="font-bold text-lg" style={{ color: colors.foreground }}>
            Registration {registrationStatus === 'success' ? 'Successful' : 'Failed'}
          </Text>
          <TouchableOpacity onPress={handleCloseRegistrationStatus} className="p-2">
            <MaterialIcons name="close" size={24} color={colors.foreground} />
          </TouchableOpacity>
        </View>

        {/* Status Content */}
        <View className="flex-1 items-center justify-center px-6">
          <View
            className="mb-6 h-20 w-20 items-center justify-center rounded-full"
            style={{
              backgroundColor: `${registrationStatus === 'success' ? '#10b981' : '#ef4444'}20`,
            }}>
            <MaterialIcons
              name={registrationStatus === 'success' ? 'check-circle' : 'error'}
              size={40}
              color={registrationStatus === 'success' ? '#10b981' : '#ef4444'}
            />
          </View>

          <Text
            className="mb-4 text-center font-bold text-2xl"
            style={{ color: colors.foreground }}>
            {registrationStatus === 'success' ? 'Registration Successful!' : 'Registration Failed'}
          </Text>

          <Text className="mb-8 text-center text-base" style={{ color: colors.grey }}>
            {registrationMessage}
          </Text>

          {registrationStatus === 'success' ? (
            <View className="w-full gap-3">
              <TouchableOpacity
                onPress={handleViewTickets}
                className="items-center rounded-lg bg-green-500 px-6 py-4">
                <Text className="font-bold text-lg text-white">View My Tickets</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleCloseRegistrationStatus}
                className="items-center rounded-lg border-2 px-6 py-4"
                style={{ borderColor: colors.grey }}>
                <Text className="font-bold text-lg" style={{ color: colors.foreground }}>
                  Back to Event
                </Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View className="w-full gap-3">
              <TouchableOpacity
                onPress={handleRetryRegistration}
                className="items-center rounded-lg px-6 py-4"
                style={{ backgroundColor: colors.primary }}>
                <Text className="font-bold text-lg text-white">Try Again</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleCloseRegistrationStatus}
                className="items-center rounded-lg border-2 px-6 py-4"
                style={{ borderColor: colors.grey }}>
                <Text className="font-bold text-lg" style={{ color: colors.foreground }}>
                  Back to Event
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    );
  }

  return (
    <BottomSheetScrollView contentContainerStyle={{ paddingBottom: 200 }}>
      <View className="p-4">
        {/* Back Button */}
        <TouchableOpacity className="mb-4 flex-row items-center" onPress={onBack}>
          <MaterialIcons name="arrow-back" size={24} color={colors.foreground} />
          <Text className="ml-2 font-medium text-base " style={{ color: colors.foreground }}>
            Back to Event
          </Text>
        </TouchableOpacity>

        <View className="mb-6">
          <Text className="light:text-light-text mb-1 font-bold text-2xl dark:text-dark-text">
            {event?.title}
          </Text>
          <Text className="light:text-light-text/70 font-medium text-base dark:text-dark-text/70">
            {formatDate(event?.startDateTime)} • {formatTime(event?.startDateTime)} to{' '}
            {formatTime(event?.endDateTime)}
          </Text>
        </View>

        <View className="mb-6">
          <Text className="light:text-light-text mb-3 font-bold text-lg dark:text-dark-text">
            Registration
          </Text>
          <Text className="light:text-light-text mb-4 text-base dark:text-dark-text">
            {ticketInfo?.isFree
              ? 'Hello! To join the event, please register below.'
              : 'Select your ticket type to continue with your purchase.'}
          </Text>

          {/* Quantity Input */}
          <View className="mb-4">
            <Text className="light:text-light-text mb-2 font-medium text-sm dark:text-dark-text">
              {ticketInfo?.isFree ? 'Number of Attendees' : 'Number of Tickets'}
            </Text>
            <View className="flex-row items-center">
              <TouchableOpacity
                onPress={() => {
                  const current = parseInt(ticketQuantity, 10) || 1;
                  if (current > 1) setTicketQuantity((current - 1).toString());
                }}
                className="h-10 w-10 items-center justify-center rounded-l-lg border border-gray-300 dark:border-gray-600"
                style={{ backgroundColor: colors.background }}>
                <MaterialIcons name="remove" size={20} color={colors.foreground} />
              </TouchableOpacity>
              <TextInput
                value={ticketQuantity}
                onChangeText={setTicketQuantity}
                keyboardType="numeric"
                className="h-10 flex-1 border-b border-t border-gray-300 px-3 text-center dark:border-gray-600"
                style={{
                  backgroundColor: colors.background,
                  color: colors.foreground,
                }}
                maxLength={2}
              />
              <TouchableOpacity
                onPress={() => {
                  const current = parseInt(ticketQuantity, 10) || 1;
                  if (current < 99) setTicketQuantity((current + 1).toString());
                }}
                className="h-10 w-10 items-center justify-center rounded-r-lg border border-gray-300 dark:border-gray-600"
                style={{ backgroundColor: colors.background }}>
                <MaterialIcons name="add" size={20} color={colors.foreground} />
              </TouchableOpacity>
            </View>
          </View>

          {ticketInfo?.isFree ? (
            <TouchableOpacity
              onPress={handleFreeRegistration}
              disabled={isProcessing}
              className={`rounded-lg py-3 ${isProcessing ? 'opacity-50' : ''} bg-light-primary dark:bg-dark-primary`}>
              <Text className="text-center font-bold text-base text-white">
                {isProcessing ? 'Processing...' : 'Register'}
              </Text>
            </TouchableOpacity>
          ) : ticketInfo?.hasLevels ? (
            <View className="space-y-3">
              {ticketInfo.levels?.map((level: any, index: number) => (
                <View
                  key={index}
                  className={`rounded-lg border p-4 ${
                    selectedTicketType === level.type
                      ? 'border-light-primary bg-light-primary/10 dark:border-dark-primary dark:bg-dark-primary/10'
                      : 'border-gray-200 dark:border-gray-700'
                  }`}>
                  <TouchableOpacity onPress={() => setSelectedTicketType(level.type)}>
                    <View className="mb-2 flex-row items-center justify-between">
                      <Text className="light:text-light-text font-bold text-base dark:text-dark-text">
                        {level.type}
                      </Text>
                      <Text className="light:text-light-primary font-bold text-base dark:text-dark-primary">
                        {event.currency} {level.price}
                      </Text>
                    </View>
                    <Text className="light:text-light-text/70 mb-3 text-sm dark:text-dark-text/70">
                      {level.quantity} tickets available
                    </Text>
                  </TouchableOpacity>
                  {selectedTicketType === level.type && (
                    <TouchableOpacity
                      onPress={() => handleTicketPurchase(level.type, level.price)}
                      disabled={isProcessing}
                      className={`rounded-lg py-2 ${isProcessing ? 'opacity-50' : ''} bg-light-primary dark:bg-dark-primary`}>
                      <Text className="text-center font-bold text-white">
                        {isProcessing
                          ? 'Processing...'
                          : `Buy ${ticketQuantity} Ticket${parseInt(ticketQuantity, 10) > 1 ? 's' : ''}`}
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              ))}
            </View>
          ) : (
            <View className="mb-4 rounded-lg border border-gray-200 p-4 dark:border-gray-700">
              <View className="mb-2 flex-row items-center justify-between">
                <Text className="light:text-light-text font-bold text-base dark:text-dark-text">
                  Standard Ticket
                </Text>
                <Text className="light:text-light-primary font-bold text-base dark:text-dark-primary">
                  {event.currency} {ticketInfo.price}
                </Text>
              </View>
              <Text className="light:text-light-text/70 mb-3 text-sm dark:text-dark-text/70">
                {ticketInfo.totalTickets} tickets available
              </Text>
              <TouchableOpacity
                onPress={() => handleTicketPurchase('Standard', Number(ticketInfo.price) || 0)}
                disabled={isProcessing}
                className={`rounded-lg py-2 ${isProcessing ? 'opacity-50' : ''} bg-light-primary dark:bg-dark-primary`}>
                <Text className="text-center font-bold text-white">
                  {isProcessing
                    ? 'Processing...'
                    : `Buy ${ticketQuantity} Ticket${parseInt(ticketQuantity, 10) > 1 ? 's' : ''}`}
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {!ticketInfo?.isFree && (
          <>
            {/* Transaction Charges Disclaimer */}
            <View className="mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-3 dark:border-yellow-800 dark:bg-yellow-900/20">
              <View className="flex-row items-start gap-2">
                <MaterialIcons name="info" size={16} color="#f59e0b" />
                <Text className="flex-1 text-xs text-yellow-800 dark:text-yellow-200">
                  <Text className="font-medium">Note:</Text> The displayed amount does not include
                  transaction charges. Additional fees may apply during payment processing.
                </Text>
              </View>
            </View>

            {/* Buyer Guarantee */}
            <View className="mb-4 flex-row items-start gap-4">
              <MaterialCommunityIcons name="shield" size={24} color={colors.foreground} />
              <View className="flex-1">
                <Text className="light:text-light-text font-medium text-subtitle dark:text-dark-text">
                  Buyer Guarantee Protected
                </Text>
                <Text className="font-regular light:text-light-text/70 text-body dark:text-dark-text/70">
                  Every ticket is protected. If your event gets canceled, we'll make it right.
                </Text>
              </View>
            </View>
          </>
        )}
      </View>
      <PaymentResultBottomSheet ref={paymentResultSheetRef} />
      <PaymentWebViewBottomSheet ref={paymentWebViewSheetRef} onClose={handleWebViewClose} />
    </BottomSheetScrollView>
  );
};

const EventMarkerSheetContent: React.FC = () => {
  const { selectedEvent, setSelectedEvent } = useEvent();
  const bottomSheetRef = useRef<BottomSheet>(null);
  const personProfileSheetRef = useRef<PersonProfileSheetHandle>(null);
  const shareBottomSheetRef = useRef<BottomSheet>(null);
  const { colors, colorScheme } = useColorScheme();
  const [showTickets, setShowTickets] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [savedEvents, setSavedEvents] = useState<string[]>([]);
  const [photoModalVisible, setPhotoModalVisible] = useState(false);
  const [selectedPhoto, setSelectedPhoto] = useState<any>(null);
  const [showShareSheet, setShowShareSheet] = useState(false);
  const insets = useSafeAreaInsets();
  const router = useRouter();

  // Get current user from store
  const currentUser = UserStore((state: any) => state.user);

  // Firebase chat hook for sharing
  const { startChat, sendMessage: sendFirebaseMessage } = useFirebaseChat();

  // Handle share button press
  const handleShareEvent = () => {
    setShowShareSheet(true);
    shareBottomSheetRef.current?.expand();
  };

  // Handle friend selection for sharing
  const handleSelectFriendForShare = async (friend: Friend, event: EventType) => {
    try {
      // Create event data to share
      const eventData = {
        id: event.id,
        title: event.title,
        description: event.description,
        location: event.location,
        startDateTime: event.startDateTime,
        endDateTime: event.endDateTime,
        coverImage: event.coverImage,
        isPaid: event.isPaid,
        currency: event.currency,
        ticketSetup: event.ticketSetup,
      };

      // Create message text with event data
      const messageText = `EVENT_SHARE:${JSON.stringify(eventData)}`;

      // Start or get existing chat
      const chatId = await startChat(friend.id);

      if (chatId) {
        // Send the event as a message (no image file for event sharing)
        await sendFirebaseMessage(chatId, friend.id, messageText, null as any);

        // Close share sheet
        setShowShareSheet(false);
        shareBottomSheetRef.current?.close();

        // Navigate to chat
        router.push({
          pathname: '/(drawer)/(tabs)/chat',
          params: {
            chatId,
            friendId: friend.id,
            friendName: friend.name,
            friendAvatar: friend.avatar,
            sharedEvent: 'true',
          },
        });

        Toast.success(`Event shared with ${friend.name}`);
      }
    } catch (error) {
      console.error('Error sharing event:', error);
      Toast.error('Failed to share event. Please try again.');
    }
  };

  // Function to open Google Maps
  const openInGoogleMaps = () => {
    if (!selectedEvent?.locationData?.coordinates) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Location coordinates not available',
        position: 'bottom',
      });
      return;
    }

    // Coordinates are stored as [longitude, latitude]
    const [longitude, latitude] = selectedEvent.locationData.coordinates;
    const address = selectedEvent.locationData.address || selectedEvent.location || '';

    let url: string;

    if (Platform.OS === 'ios') {
      // For iOS, use Apple Maps or Google Maps app
      url = `maps://app?daddr=${latitude},${longitude}`;
      // Alternative Google Maps URL for iOS: `comgooglemaps://?daddr=${latitude},${longitude}`
    } else {
      // For Android, use Google Maps
      url = `geo:${latitude},${longitude}?q=${latitude},${longitude}(${encodeURIComponent(address)})`;
    }

    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          // Fallback to web Google Maps
          const webUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
          return Linking.openURL(webUrl);
        }
      })
      .catch((err) => {
        console.error('Error opening maps:', err);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Could not open maps application',
          position: 'bottom',
        });
      });
  };

  // Fetch saved events for the user
  useEffect(() => {
    const fetchSavedEvents = async () => {
      if (currentUser?.id) {
        try {
          const response = await EventService.getSavedEvents(currentUser.id);
          if (response.success && response.body) {
            // Extract event IDs from the saved events

            const eventIds = response.body.events.map(
              (event: any) => event.id || event.eventId || event._id
            );
            setSavedEvents(eventIds);
          }
        } catch (error) {
          console.error('Error fetching saved events:', error);
        }
      }
    };

    fetchSavedEvents();
  }, [currentUser?.id]);

  // Check if the selected event is saved when selectedEvent or savedEvents change
  useEffect(() => {
    if (selectedEvent && savedEvents.length > 0) {
      const isEventSaved = savedEvents.includes(selectedEvent.id.toString());
      setIsSaved(isEventSaved);
    } else {
      setIsSaved(false);
    }
  }, [selectedEvent, savedEvents]);

  // Handle save/unsave event
  const handleSaveEvent = async () => {
    if (!selectedEvent || !currentUser?.id) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Unable to save event. Please try again.',
        position: 'bottom',
      });
      return;
    }

    setIsSaving(true);
    try {
      await EventService.saveEvent(currentUser.id, selectedEvent.id.toString());

      // Update local saved events state
      if (isSaved) {
        // Remove from saved events
        setSavedEvents((prev) => prev.filter((id) => id !== selectedEvent.id.toString()));
      } else {
        // Add to saved events
        setSavedEvents((prev) => [...prev, selectedEvent.id.toString()]);
      }

      setIsSaved(!isSaved);

      /*  Toast.show({
        type: 'success',
        text1: isSaved ? 'Event Unsaved' : 'Event Saved',
        text2: isSaved
          ? 'Event removed from your saved events'
          : 'Event added to your saved events',
        position: 'bottom',
      }); */
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.message || 'Failed to save event. Please try again.',
        position: 'bottom',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handlePhotoPress = (photo: any) => {
    setSelectedPhoto(photo);
    setPhotoModalVisible(true);
  };

  const handleEventImagePress = () => {
    if (selectedEvent) {
      const imageData = {
        id: selectedEvent.id || 'event-image',
        secureUrl: getEventImage(),
        publicId: selectedEvent.coverImage || 'event-cover',
      };
      handlePhotoPress(imageData);
    }
  };

  useEffect(() => {
    if (selectedEvent) {
      console.log(selectedEvent);
      bottomSheetRef.current?.expand();
    } else {
      bottomSheetRef.current?.close();
      setShowTickets(false);
    }
  }, [selectedEvent]);

  // Handle back button to close the sheet when it's open
  useEffect(() => {
    const handleBackPress = () => {
      if (selectedEvent) {
        // Close the bottom sheet
        setSelectedEvent(null);
        setShowTickets(false);
        return true; // Prevent default back behavior
      }
      return false; // Let default back behavior happen
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);

    return () => {
      backHandler.remove();
    };
  }, [selectedEvent]);

  const handleSheetChanges = (index: number) => {
    if (index === -1) {
      setSelectedEvent(null);
      setShowTickets(false);
    }
  };

  const getEventImage = () => {
    // Use a default image if no cover image is provided
    if (!selectedEvent?.coverImage) {
      const eventTypeImages: Record<string, string> = {
        Business:
          'https://images.unsplash.com/photo-1565398305935-49e5dcc5c9f6?q=80&w=1920&auto=format',
        Leisure:
          'https://images.unsplash.com/photo-1517457373958-b7bdd4587205?q=80&w=1920&auto=format',
        Entertainment:
          'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?q=80&w=1920&auto=format',
        Educational:
          'https://images.unsplash.com/photo-1524178232363-1fb2b075b655?q=80&w=1920&auto=format',
      };
      const eventType = selectedEvent?.eventType || '';
      return (
        eventTypeImages[eventType] || 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4'
      );
    }
    return selectedEvent.coverImage;
  };

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={selectedEvent ? 0 : -1}
      snapPoints={['100%']}
      enablePanDownToClose
      onClose={() => {
        setSelectedEvent(null);
        setShowTickets(false);
      }}
      onChange={handleSheetChanges}
      backgroundStyle={{ backgroundColor: colors.background }}
      enableOverDrag={false}
      backdropComponent={RenderBackdrop}
      containerStyle={{
        zIndex: 100,
        elevation: 100,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
      handleIndicatorStyle={{
        backgroundColor: colors.foreground,
      }}
      topInset={insets.top}
      bottomInset={0}>
      {showTickets && selectedEvent ? (
        <TicketsContent event={selectedEvent} onBack={() => setShowTickets(false)} />
      ) : (
        <BottomSheetScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 20 }}>
          <View
            className="flex-row items-center justify-between border-b px-4 py-4"
            style={{ borderBottomColor: colors.grey5 }}>
            <Text className="text-lg font-semibold" style={{ color: colors.foreground }}>
              Event Details
            </Text>
            <TouchableOpacity
              className="p-2"
              onPress={() => {
                setSelectedEvent(null);
                setShowTickets(false);
              }}>
              <Ionicons name="close" size={24} color={colors.foreground} />
            </TouchableOpacity>
          </View>

          {/* Event Header Image */}
          <TouchableOpacity
            className="relative h-48 w-full"
            onPress={handleEventImagePress}
            activeOpacity={0.9}>
            <Image
              source={{ uri: getEventImage() }}
              className="h-full w-full"
              style={{ resizeMode: 'cover' }}
            />

            {/* Event Status Chip */}
            {(() => {
              const eventStatus = getEventStatus(
                selectedEvent?.startDateTime,
                selectedEvent?.endDateTime
              );

              if (!eventStatus.status) return null;

              return (
                <View
                  className="absolute right-2 top-2 rounded-full px-2 py-1"
                  style={{ backgroundColor: eventStatus.backgroundColor }}>
                  <Text className="font-bold text-xs text-white">{eventStatus.status}</Text>
                </View>
              );
            })()}

            <View className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
              <Text className="mb-1 font-bold text-2xl uppercase text-white">
                {selectedEvent?.title}
              </Text>
              <Text className="text-base text-white">{selectedEvent?.location}</Text>
            </View>
          </TouchableOpacity>

          {/* Event Details */}
          <View className="p-4">
            <Text className="light:text-light-text mb-4 font-bold text-2xl dark:text-dark-text">
              {selectedEvent?.location} - {selectedEvent?.title}
            </Text>

            {/* Date and Time */}
            <View className="mb-6 flex-row items-center justify-between">
              <View className="flex-1">
                <Text className="light:text-light-text font-medium text-lg dark:text-dark-text">
                  {selectedEvent?.startDateTime
                    ? format(new Date(selectedEvent.startDateTime), 'EEEE')
                    : ''}
                </Text>

                <Text className="light:text-light-text/70 text-base dark:text-dark-text/70">
                  {selectedEvent?.startDateTime
                    ? format(new Date(selectedEvent.startDateTime), 'MMMM d, h:mm a')
                    : ''}{' '}
                  to{' '}
                  {selectedEvent?.endDateTime
                    ? format(new Date(selectedEvent.endDateTime), 'h:mm a')
                    : ''}
                </Text>
                <Text className="light:text-light-text/70 mb-2 text-base dark:text-dark-text/70">
                  {selectedEvent?.locationData.address || selectedEvent?.location}
                </Text>
              </View>
              <View className="items-end">
                <View className="flex items-center">
                  <Text className="light:text-light-text font-bold text-lg dark:text-dark-text">
                    {selectedEvent ? calculateTicketStats(selectedEvent).attendees : 0}
                  </Text>
                  <Text className="light:text-light-text/70 text-xs dark:text-dark-text/70">
                    attending
                  </Text>
                </View>
              </View>
            </View>

            {/* Location */}
            <View className="mb-6">
              {selectedEvent?.locationData && (
                <LocationPreview
                  location={{
                    coordinates: selectedEvent.locationData.coordinates,
                    manualAddress: selectedEvent.locationData.address,
                    name: selectedEvent.locationData.name,
                    address: selectedEvent.locationData.address || '',
                  }}
                  editable={false}
                />
              )}

              {/* View Full Map Button */}
              {selectedEvent?.locationData?.coordinates && (
                <TouchableOpacity
                  onPress={openInGoogleMaps}
                  className="mt-3 flex-row items-center justify-center rounded-lg border border-gray-300 px-4 py-2 dark:border-gray-600"
                  style={{ backgroundColor: 'transparent' }}>
                  <MaterialIcons
                    name="map"
                    size={18}
                    color={colors.primary}
                    style={{ marginRight: 8 }}
                  />
                  <Text className="font-medium text-sm" style={{ color: colors.primary }}>
                    View Full Map
                  </Text>
                </TouchableOpacity>
              )}
            </View>

            {/* Registration Section */}
            <View className="mb-6">
              <View className="mb-3 flex-row gap-3">
                <TouchableOpacity
                  onPress={() => setShowTickets(true)}
                  className="flex-1 flex-row items-center justify-center rounded-full bg-light-primary px-4 py-3 dark:bg-dark-primary">
                  <MaterialIcons
                    name="how-to-reg"
                    size={20}
                    color="white"
                    style={{ marginRight: 8 }}
                  />
                  <Text className="text-center font-bold text-base text-white">
                    {selectedEvent?.isPaid ? 'View Tickets' : 'Register'}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={handleSaveEvent}
                  disabled={isSaving}
                  className="flex-row items-center justify-center rounded-full border-2 px-4 py-3"
                  style={{
                    borderColor: colors.primary,
                    backgroundColor: isSaved ? colors.primary : 'transparent',
                    opacity: isSaving ? 0.7 : 1,
                  }}>
                  <Ionicons
                    name={isSaved ? 'bookmark' : 'bookmark-outline'}
                    size={20}
                    color={isSaved ? 'white' : colors.primary}
                  />
                  <Text
                    className="ml-2 font-bold text-sm"
                    style={{ color: isSaved ? 'white' : colors.primary }}>
                    {isSaving ? 'Saving...' : isSaved ? 'Saved' : 'Save'}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Share Button */}
              <TouchableOpacity
                onPress={handleShareEvent}
                className="flex-row items-center justify-center rounded-full border-2 px-4 py-3"
                style={{
                  borderColor: colors.primary,
                  backgroundColor: 'transparent',
                }}>
                <Ionicons name="share-outline" size={20} color={colors.primary} />
                <Text className="ml-2 font-bold text-sm" style={{ color: colors.primary }}>
                  Share Event
                </Text>
              </TouchableOpacity>
            </View>

            {/* About Event Section */}
            <View className="mb-6">
              <Text className="light:text-light-text mb-2 font-bold text-lg dark:text-dark-text">
                About Event
              </Text>
              <Text className="light:text-light-text text-base dark:text-dark-text">
                {selectedEvent?.description}
              </Text>
            </View>

            {/* Event Uploads Section */}
            {(selectedEvent as any)?.eventUploads &&
              (selectedEvent as any).eventUploads.length > 0 && (
                <View className="mb-6">
                  <Text className="light:text-light-text mb-3 font-bold text-lg dark:text-dark-text">
                    Event Photos
                  </Text>
                  <View className="-mx-1 flex-row flex-wrap">
                    {(selectedEvent as any).eventUploads.map((upload: any, index: number) => (
                      <TouchableOpacity
                        key={upload.id || index}
                        className="mb-2 w-1/3 px-1"
                        onPress={() => handlePhotoPress(upload)}>
                        <Image
                          source={{ uri: upload.secureUrl }}
                          className="aspect-square w-full rounded-lg"
                          style={{ resizeMode: 'cover' }}
                        />
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              )}

            {/* Hosts Section */}
            <View className="mb-6">
              <Text className="light:text-light-text mb-3 font-bold text-lg dark:text-dark-text">
                Host
              </Text>

              <TouchableOpacity
                className="mb-2 flex-row items-center"
                onPress={() => {
                  // Type assertion to handle extended event properties
                  const eventWithUser = selectedEvent as any;
                  if (eventWithUser?.user?.id && eventWithUser?.user?.email) {
                    // Close the current bottom sheet first
                    setSelectedEvent(null);
                    setShowTickets(false);
                    // Navigate to view profile screen
                    router.push({
                      pathname: '/Auth/viewProfile',
                      params: {
                        userId: eventWithUser.user.id.toString(),
                        email: eventWithUser.user.email.toString(),
                      },
                    });
                  }
                }}>
                <Image
                  source={{
                    uri:
                      (selectedEvent as any)?.user?.profilePicture &&
                      Array.isArray((selectedEvent as any)?.user.profilePicture) &&
                      (selectedEvent as any)?.user.profilePicture.length > 0 &&
                      (selectedEvent as any)?.user.profilePicture[
                        (selectedEvent as any)?.user.profilePicture.length - 1
                      ]?.secureUrl
                        ? (selectedEvent as any)?.user.profilePicture[
                            (selectedEvent as any)?.user.profilePicture.length - 1
                          ].secureUrl
                        : 'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                  }}
                  className="mr-3 h-10 w-10 rounded-full"
                  style={{ resizeMode: 'cover' }}
                />
                <View className="flex-1">
                  <Text className="light:text-light-text font-medium text-base dark:text-dark-text">
                    {(selectedEvent as any)?.user?.fullName || 'Event Host'}
                  </Text>
                  <View className="mt-1 flex-row items-center">
                    {/* Display 5 stars with random rating for demo purposes */}
                    {[1, 2, 3, 4, 5].map((star) => {
                      // Generate a random rating between 3-5 for demo
                      const rating = 3 + Math.floor(Math.random() * 2.1);
                      return (
                        <Ionicons
                          key={star}
                          name={star <= rating ? 'star' : 'star-outline'}
                          size={14}
                          color="#FFD700"
                          style={{ marginRight: 2 }}
                        />
                      );
                    })}
                    <Text className="light:text-light-text/70 ml-1 text-xs dark:text-dark-text/70">
                      {(3 + Math.random() * 2).toFixed(1)}/5.0
                    </Text>
                  </View>
                </View>
                <Ionicons
                  name="chevron-forward"
                  size={20}
                  color={colors.grey}
                  style={{ marginLeft: 8 }}
                />
              </TouchableOpacity>
            </View>
          </View>
        </BottomSheetScrollView>
      )}
      <PersonProfileSheet ref={personProfileSheetRef} />
      <PhotoViewModal
        visible={photoModalVisible}
        photo={selectedPhoto}
        onClose={() => setPhotoModalVisible(false)}
      />

      {/* Share Event Bottom Sheet */}
      <BottomSheet
        ref={shareBottomSheetRef}
        index={-1}
        snapPoints={['90%']}
        enablePanDownToClose
        backdropComponent={RenderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: colors.foreground,
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <EventShareBottomSheet
          bottomSheetRef={shareBottomSheetRef}
          event={selectedEvent}
          onSelectFriend={handleSelectFriendForShare}
          colors={colors}
          isDark={colorScheme === 'dark'}
          isOpen={showShareSheet}
          onClose={() => {
            setShowShareSheet(false);
            shareBottomSheetRef.current?.close();
          }}
        />
      </BottomSheet>
    </BottomSheet>
  );
};

export default function EventMarkerSheet() {
  return (
    <View
      style={{
        position: 'absolute',
        zIndex: 100,
        width: '100%',
        height: '100%',
        pointerEvents: 'box-none',
      }}>
      <EventMarkerSheetContent />
    </View>
  );
}
