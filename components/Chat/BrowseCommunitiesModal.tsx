import { Ionicons } from '@expo/vector-icons';
import { BottomSheetView, BottomSheetBackdrop, BottomSheetModal } from '@gorhom/bottom-sheet';
import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import {
  View,
  TouchableOpacity,
  FlatList,
  Image,
  TextInput,
  ActivityIndicator,
} from 'react-native';

import { CommunityService } from '../../services/CommunityService';
import { UserStore } from '../../store/store';

import { Text } from '~/components/nativewindui/Text';
import { Box } from '~/components/ui/box';

interface Community {
  id: string;
  communityName: string;
  communityDescription: string;
  communityImages: {
    secureUrl: string;
  }[];
  user: {
    fullName: string;
  };
}

interface BrowseCommunitiesModalProps {
  visible: boolean;
  onClose: () => void;
  onJoinCommunity: (community: Community) => void;
  onJoinError?: (error: string) => void;
  colors: any;
}

const BrowseCommunitiesModal = ({
  visible,
  onClose,
  onJoinCommunity,
  onJoinError,
  colors,
}: BrowseCommunitiesModalProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [communities, setCommunities] = useState<Community[]>([]);
  const [joiningCommunities, setJoiningCommunities] = useState<Set<string>>(new Set());

  // Ref for bottom sheet modal
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);

  // Define snap points
  const snapPoints = useMemo(() => ['95%'], []);

  const userData = UserStore((state: any) => state.user);

  const loadCommunities = useCallback(async () => {
    if (!userData?.id) return;

    try {
      setLoading(true);

      // Load all communities
      const communitiesResponse = await CommunityService.getCommunities(userData.id);

      if (communitiesResponse.success) {
        const allCommunities = communitiesResponse.body?.communities || [];
        setCommunities(allCommunities);
      }
    } catch (error) {
      console.error('Error loading communities:', error);
      setCommunities([]);
    } finally {
      setLoading(false);
    }
  }, [userData?.id]);

  const handleJoinCommunity = async (community: Community) => {
    if (!userData?.id || joiningCommunities.has(community.id)) return;

    try {
      // Add community ID to joining set
      setJoiningCommunities((prev) => new Set(prev).add(community.id));

      // Call the API to join the community
      const response = await CommunityService.joinCommunity(community.id, userData.id);

      if (response.success) {
        // Remove the community from the list since user has joined
        setCommunities((prev) => prev.filter((c) => c.id !== community.id));

        // Call the parent's onJoinCommunity callback
        onJoinCommunity(community);
      } else {
        throw new Error(response.message || 'Failed to join community');
      }
    } catch (error) {
      console.error('Error joining community:', error);
      // Call the error callback if provided
      if (onJoinError) {
        onJoinError(error instanceof Error ? error.message : 'Failed to join community');
      }
    } finally {
      // Remove community ID from joining set
      setJoiningCommunities((prev) => {
        const newSet = new Set(prev);
        newSet.delete(community.id);
        return newSet;
      });
    }
  };

  useEffect(() => {
    if (visible) {
      bottomSheetModalRef.current?.present();
      loadCommunities();
    } else {
      bottomSheetModalRef.current?.dismiss();
    }
  }, [visible, loadCommunities]);

  const handleSheetChanges = useCallback(
    (index: number) => {
      if (index === -1) {
        onClose();
      }
    },
    [onClose]
  );

  const filteredCommunities = communities.filter(
    (community) =>
      community.communityName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      community.communityDescription.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderCommunityItem = ({ item }: { item: Community }) => {
    const isJoining = joiningCommunities.has(item.id);

    return (
      <View className="flex-row border-b p-4" style={{ borderBottomColor: colors.grey5 }}>
        <Image
          source={{
            uri:
              item.communityImages?.[0]?.secureUrl ||
              'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
          }}
          className="h-[60px] w-[60px] rounded-full"
        />
        <View className="ml-3 flex-1 justify-center">
          <Text className="mb-1 text-base font-semibold" style={{ color: colors.foreground }}>
            {item.communityName}
          </Text>
          <Text className="mb-1.5 text-sm" style={{ color: colors.grey }} numberOfLines={2}>
            {item.communityDescription}
          </Text>
          <Text className="text-xs" style={{ color: colors.grey }}>
            Created by {item.user.fullName}
          </Text>
        </View>
        <TouchableOpacity
          className="ml-2 self-center rounded-full px-4 py-2"
          style={{
            backgroundColor: isJoining ? colors.grey : colors.primary,
            opacity: isJoining ? 0.7 : 1,
          }}
          onPress={() => handleJoinCommunity(item)}
          disabled={isJoining}>
          {isJoining ? (
            <View className="flex-row items-center">
              <ActivityIndicator size="small" color="white" className="mr-2" />
              <Text className="font-semibold text-white">Joining...</Text>
            </View>
          ) : (
            <Text className="font-semibold text-white">Join</Text>
          )}
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <BottomSheetModal
      ref={bottomSheetModalRef}
      index={0}
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      backdropComponent={(props) => (
        <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} opacity={0.6} />
      )}
      backgroundStyle={{ backgroundColor: colors.background }}
      handleIndicatorStyle={{ backgroundColor: colors.grey }}>
      <BottomSheetView className="flex-1" style={{ backgroundColor: colors.background }}>
        <View
          className="flex-row items-center justify-between border-b px-4 py-4"
          style={{ borderBottomColor: colors.grey5 }}>
          {/*    <TouchableOpacity className="items-center justify-center w-10 h-10" onPress={onClose}>
            <Ionicons name="arrow-back" size={24} color={colors.foreground} />
          </TouchableOpacity> */}
          <Text className="text-lg font-semibold" style={{ color: colors.foreground }}>
            Browse Communities
          </Text>
          <View className="w-10" />
        </View>

        <Box className="p-4">
          <View
            className="flex-row items-center rounded-lg px-3"
            style={{ backgroundColor: colors.card }}>
            <Ionicons name="search" size={20} color={colors.grey} className="mr-2" />
            <TextInput
              className="h-[46px] flex-1 text-base"
              placeholder="Search communities"
              placeholderTextColor={colors.grey}
              value={searchQuery}
              onChangeText={setSearchQuery}
              style={{ color: colors.foreground }}
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => setSearchQuery('')} className="p-1">
                <Ionicons name="close-circle" size={20} color={colors.grey} />
              </TouchableOpacity>
            ) : null}
          </View>
        </Box>

        {loading ? (
          <View className="flex-1 items-center justify-center">
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : (
          <FlatList
            data={filteredCommunities}
            keyExtractor={(item) => item.id.toString()}
            renderItem={renderCommunityItem}
            className="flex-1"
            contentContainerStyle={{ paddingBottom: 20 }}
            ListEmptyComponent={
              <View className="flex-1 items-center justify-center pt-[100px]">
                <Ionicons name="search-outline" size={64} color={colors.grey} />
                <Text className="mt-4 text-base" style={{ color: colors.foreground }}>
                  No communities found
                </Text>
              </View>
            }
          />
        )}
      </BottomSheetView>
    </BottomSheetModal>
  );
};

export default BrowseCommunitiesModal;
